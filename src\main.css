@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');
@import 'tailwindcss';

/* 全局样式重置和暗色调主题 */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  background-color: #0f172a;
  color: #e2e8f0;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 暗色调根元素 */
#app {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* 按钮样式 */
button {
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 输入框样式 */
input,
textarea,
select {
  transition: all 0.2s ease-in-out;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  border-color: #3b82f6;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Firefox 滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: #475569 #1e293b;
}

/* 自定义断点 - 1000px */
@media (min-width: 1000px) {
  .custom-mobile\:hidden {
    display: none;
  }
  .custom-mobile\:relative {
    position: relative;
  }
  .custom-mobile\:translate-x-0 {
    transform: translateX(0);
  }
}

/* Electron specific styles */
.electron-titlebar {
  -webkit-user-select: none;
  user-select: none;
  background: #0f172a;
  border-bottom: 1px solid #334155;
}

/* 隐藏Electron环境下的滚动条 */
.electron-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.electron-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 玻璃态效果 */
.glass {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.glass-light {
  background: rgba(51, 65, 85, 0.6);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(148, 163, 184, 0.15);
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
}

.gradient-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #22c55e 0%, #15803d 100%);
}

.gradient-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.gradient-error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

/* 文本渐变 */
.text-gradient {
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 悬停效果 */
.hover-lift {
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* 加载动画 */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%,
  20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%,
  100% {
    content: '...';
  }
}

/* 响应式工具类 */
@media (max-width: 640px) {
  .mobile-compact {
    padding: 0.75rem;
  }

  .mobile-text-sm {
    font-size: 0.875rem;
  }
}
