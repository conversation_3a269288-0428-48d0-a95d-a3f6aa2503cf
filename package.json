{"name": "tools", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "main": "electron/main.js", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "electron:dev": "node electron/dev-runner.js", "electron:build": "node electron/build.js", "signaling-server": "node signaling-server.js", "test:unit": "vitest"}, "dependencies": {"@imgly/background-removal": "^1.7.0", "@material-tailwind/html": "^3.0.0-beta.7", "@tailwindcss/vite": "^4.1.12", "@types/qrcode": "^1.5.5", "crypto-js": "^4.2.0", "dompurify": "^3.2.6", "fabric": "^6.7.1", "gif.js": "^0.2.0", "gifuct-js": "^2.1.2", "jsonpath-plus": "^10.3.0", "jsqr": "^1.4.0", "jszip": "^3.10.1", "pdfjs-dist": "^5.4.54", "qrcode": "^1.5.4", "vue": "^3.5.18", "vue-advanced-cropper": "^2.8.9", "vue-easy-lightbox": "^1.19.0", "vue-i18n": "^9.14.5", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0", "ws": "^8.18.3", "xlsx": "^0.18.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tsconfig/node22": "^22.0.2", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "electron": "^31.7.7", "electron-builder": "^24.13.3", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "less": "^4.4.1", "npm-run-all2": "^8.0.4", "postcss": "^8.5.6", "prettier": "3.6.2", "tailwindcss": "^4.1.12", "typescript": "~5.8.0", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "vitest": "^3.2.4", "vue-tsc": "^3.0.4"}, "volta": {"node": "20.19.4"}}